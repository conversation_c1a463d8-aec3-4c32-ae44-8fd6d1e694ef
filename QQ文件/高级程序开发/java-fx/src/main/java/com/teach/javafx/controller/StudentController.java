// 分析结论：
// personId 并非始终为 null，其值由以下三个关键操作决定：

// 1. 表格行选择时赋值（关键触发点）
public void onTableRowSelect(ListChangeListener.Change<? extends Integer> change) {
    changeStudentInfo();  // 会从选中行获取 personId
}

// 2. 新增操作时置空
@FXML
protected void onAddButtonClick() {
    clearPanel();  // 会置 personId = null
}

// 3. 删除操作时赋值
@FXML
protected void onDeleteButtonClick() {
    Map form = dataTableView.getSelectionModel().getSelectedItem();
    if (form == null) return;
    personId = CommonMethod.getInteger(form, "personId");  // 删除时也会获取 personId
}

// 安全性建议：新增字段校验逻辑
private boolean validatePersonId() {
    if(personId == null) {
        MessageDialog.showDialog("请先选择学生");
        return false;
    }
    return true;
}

// 修改后的 onFamilyButtonClick 方法（增强可读性）
protected void onFamilyButtonClick() {
    if(!validatePersonId()) return;  // 使用统一校验方法
    
    // 原有业务逻辑保持不变...
}