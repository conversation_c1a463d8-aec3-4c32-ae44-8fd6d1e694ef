<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.teach.javafx.controller.CourseSelectionController">
   <top>
      <VBox BorderPane.alignment="CENTER">
         <children>
            <!-- 标题区域 -->
            <HBox alignment="CENTER" style="-fx-background-color: #2196F3; -fx-padding: 15;">
               <children>
                  <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="选课管理" />
               </children>
            </HBox>
            
            <!-- 查询区域 -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f5f5f5; -fx-padding: 15;">
               <children>
                  <Label style="-fx-font-weight: bold;" text="查询条件:" />
                  <TextField fx:id="numNameTextField" promptText="请输入学号、姓名、课程编号或课程名称" prefWidth="300.0" />
                  <Button onAction="#onQueryButtonClick" style="-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 20;" text="查询" />
               </children>
            </HBox>
            
            <!-- 操作区域 -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #ffffff; -fx-padding: 15; -fx-border-color: #e0e0e0; -fx-border-width: 0 0 1 0;">
               <children>
                  <Label style="-fx-font-weight: bold;" text="添加选课:" />
                  <Label text="学生:" />
                  <ComboBox fx:id="studentComboBox" prefWidth="200.0" promptText="请选择学生" />
                  <Label text="课程:" />
                  <ComboBox fx:id="courseComboBox" prefWidth="250.0" promptText="请选择课程" />
                  <Button onAction="#onAddButtonClick" style="-fx-background-color: #FF9800; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 8 20;" text="添加选课" />
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <!-- 表格区域 -->
      <TableView fx:id="dataTableView" style="-fx-background-color: white; -fx-border-color: #e0e0e0; -fx-border-width: 1;">
         <columns>
            <TableColumn fx:id="studentNumColumn" prefWidth="100.0" text="学号" />
            <TableColumn fx:id="studentNameColumn" prefWidth="100.0" text="姓名" />
            <TableColumn fx:id="classNameColumn" prefWidth="120.0" text="班级" />
            <TableColumn fx:id="majorColumn" prefWidth="150.0" text="专业" />
            <TableColumn fx:id="courseNumColumn" prefWidth="120.0" text="课程编号" />
            <TableColumn fx:id="courseNameColumn" prefWidth="200.0" text="课程名称" />
            <TableColumn fx:id="creditColumn" prefWidth="80.0" text="学分" />
            <TableColumn fx:id="selectionTimeColumn" prefWidth="150.0" text="选课时间" />
            <TableColumn fx:id="operateColumn" prefWidth="150.0" text="操作" />
         </columns>
         <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
         </columnResizePolicy>
      </TableView>
   </center>
   
   <bottom>
      <!-- 状态栏 -->
      <HBox alignment="CENTER_LEFT" style="-fx-background-color: #f5f5f5; -fx-padding: 10; -fx-border-color: #e0e0e0; -fx-border-width: 1 0 0 0;">
         <children>
            <Label style="-fx-font-size: 12px; -fx-text-fill: #666666;" text="选课管理系统 - 支持学生选课记录的增删改查操作" />
         </children>
      </HBox>
   </bottom>
</BorderPane>
