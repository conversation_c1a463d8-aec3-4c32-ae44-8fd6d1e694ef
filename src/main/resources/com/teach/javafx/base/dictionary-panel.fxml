<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TreeTableColumn?>
<?import javafx.scene.control.TreeTableView?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.FlowPane?>
<!-- 数据字典维护面板FXML配置文件 com.teach.javafx.controller.base.DictionaryController -->
<BorderPane xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.teach.javafx.controller.base.DictionaryController">
    <top>
        <FlowPane nodeOrientation="LEFT_TO_RIGHT" prefHeight="40.0" prefWidth="600.0" BorderPane.alignment="CENTER">
            <children>
                <Button mnemonicParsing="false" onAction="#onAddButtonClick" text="添加一行">
                    <FlowPane.margin>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                    </FlowPane.margin>
                </Button>
                <Button mnemonicParsing="false" onAction="#onSaveButtonClick" text="修改保存">
                    <FlowPane.margin>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                    </FlowPane.margin>
                </Button>
                <Button mnemonicParsing="false" onAction="#onDeleteButtonClick" text="删除">
                    <FlowPane.margin>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                    </FlowPane.margin></Button>
            </children>
            <padding>
                <Insets bottom="3.0" left="3.0" right="3.0" top="3.0" />
            </padding>
        </FlowPane>
    </top>
    <center>
        <TreeTableView fx:id="treeTable">
         <columns>
             <TreeTableColumn fx:id="idColumn" prefWidth="75.0" text="主键" />
             <TreeTableColumn fx:id="valueColumn" prefWidth="140.0" text="字典值" />
             <TreeTableColumn fx:id="titleColumn" prefWidth="213.5" text="字典名" />
         </columns>
        </TreeTableView>
    </center>
</BorderPane>
