<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.FlowPane?>
<?import javafx.scene.text.TextFlow?>

<!-- 消息提示对话框FXML配置文件 对应页面交互控制类 com.teach.javafx.controller.base.MessageController -->
<BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="260.0" prefWidth="300.0" xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.teach.javafx.controller.base.MessageController">
   <center>
      <TextFlow fx:id="textFLow" prefHeight="200.0" prefWidth="300.0" BorderPane.alignment="CENTER" />
   </center>
   <bottom>
      <FlowPane alignment="CENTER" prefHeight="40.0" prefWidth="300.0" BorderPane.alignment="CENTER">
         <children>
            <Button  mnemonicParsing="false" text="确认" onAction="#okButtonClick">
               <padding>
                  <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
               </padding>
            </Button>
         </children>
      </FlowPane>
   </bottom>
</BorderPane>
