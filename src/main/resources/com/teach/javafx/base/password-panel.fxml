<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<!-- 密码修改面板FXML配置文件 对应页面交互控制类 com.teach.javafx.controller.base.PasswordController -->
<VBox alignment="CENTER" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="200.0" prefWidth="300.0" spacing="20.0" xmlns="http://javafx.com/javafx/17" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.teach.javafx.controller.base.PasswordController">
    <padding>
        <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
    </padding>
   <GridPane>
     <columnConstraints>
       <ColumnConstraints hgrow="SOMETIMES" maxWidth="93.0" minWidth="10.0" prefWidth="48.0" />
       <ColumnConstraints hgrow="SOMETIMES" maxWidth="197.0" minWidth="10.0" prefWidth="197.0" />
     </columnConstraints>
     <rowConstraints>
       <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
       <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
         <RowConstraints />
     </rowConstraints>
      <children>
         <Label alignment="CENTER_RIGHT" text="旧密码" GridPane.rowIndex="0" />
         <PasswordField fx:id="oldPasswordField" GridPane.columnIndex="1" GridPane.rowIndex="0" />
          <Label alignment="CENTER_RIGHT" text="新密码" GridPane.rowIndex="1" />
          <PasswordField fx:id="newPasswordField" GridPane.columnIndex="1" GridPane.rowIndex="1" />
          <Label alignment="CENTER_RIGHT" text="确认密码" GridPane.rowIndex="2" />
          <PasswordField fx:id="confirmPasswordField" GridPane.columnIndex="1" GridPane.rowIndex="2" />
      </children>
   </GridPane>
    <Button onAction="#onSubmitButtonClick" text="提交" />
</VBox>
