<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane prefWidth="800.0" xmlns="http://javafx.com/javafx/17.0.12" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.teach.javafx.controller.FamilyMemberController">
    <top>
        <HBox id="HBox" alignment="CENTER_LEFT" spacing="5.0">
            <padding>
                <Insets bottom="3.0" left="3.0" right="3.0" top="3.0" />
            </padding>
            <FlowPane prefHeight="40.0" prefWidth="200.0" BorderPane.alignment="CENTER">
                <Button mnemonicParsing="false" onAction="#onAddButtonClick" text="添加">
                    <FlowPane.margin>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                    </FlowPane.margin>
                </Button>
                <Button mnemonicParsing="false" onAction="#onDeleteButtonClick" text="删除">
                    <FlowPane.margin>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                    </FlowPane.margin>
                </Button>

            </FlowPane>
            <Pane prefHeight="-1.0" prefWidth="-1.0" HBox.hgrow="ALWAYS" />
            <FlowPane alignment="TOP_RIGHT" prefHeight="40.0" prefWidth="400.0" BorderPane.alignment="CENTER">
                <Label prefWidth="49.0" text="关系姓名">
                    <FlowPane.margin>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                    </FlowPane.margin>
                </Label>
                <TextField fx:id="relationNameTextField" prefWidth="100.0">
                    <FlowPane.margin>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                    </FlowPane.margin>
                </TextField>
                <Button mnemonicParsing="false" onAction="#onQueryButtonClick" text="查询">
                    <FlowPane.margin>
                        <Insets bottom="5.0" left="5.0" right="5.0" top="5.0" />
                    </FlowPane.margin>
                </Button>
            </FlowPane>
        </HBox>
    </top>
    <center>
        <SplitPane dividerPositions="0.65">
            <TableView fx:id="dataTableView">
                <columns>
                    <TableColumn fx:id="relationColumn" prefWidth="100.0" text="关系" />
                    <TableColumn fx:id="nameColumn" prefWidth="100.0" text="姓名" />
                    <TableColumn fx:id="genderColumn" prefWidth="55.0" text="性别" />
                    <TableColumn fx:id="phoneColumn" prefWidth="120.0" text="电话" />
                    <TableColumn fx:id="ageColumn" prefWidth="75.0" text="年龄" />
                    <TableColumn fx:id="unitColumn" prefWidth="200.0" text="单位" />


                </columns>
            </TableView>
            <VBox alignment="TOP_CENTER" prefWidth="350.0" spacing="15.0">
                <padding>
                    <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                </padding>
                <GridPane hgap="10.0" vgap="10.0">
                    <columnConstraints>
                        <ColumnConstraints halignment="RIGHT" hgrow="SOMETIMES" maxWidth="124.0" minWidth="10.0" prefWidth="67.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="193.0" minWidth="10.0" prefWidth="193.0" />
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="100" minWidth="10.0" prefWidth="100" />
                    </columnConstraints>
                    <rowConstraints>
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                    </rowConstraints>
                    <children>
                        <Label text="关系" GridPane.rowIndex="0" />
                        <TextField fx:id="relationField" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        <Label text="姓名" GridPane.rowIndex="1" />
                        <TextField fx:id="nameField" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        <Label text="电话" GridPane.rowIndex="2" />
                        <TextField fx:id="phoneField" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        <Label text="年龄" GridPane.rowIndex="3" />
                        <TextField fx:id="ageField" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        <Label text="单位" GridPane.rowIndex="4" />
                        <TextField fx:id="unitField" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        <Label text="性别" GridPane.rowIndex="5" />
                        <ComboBox fx:id="genderComboBox" GridPane.columnIndex="1" GridPane.rowIndex="5" />


                    </children>
                </GridPane>
                <FlowPane alignment="CENTER" columnHalignment="CENTER" prefHeight="36.0">
                    <Button onAction="#onSaveButtonClick" text="保存">
                        <FlowPane.margin>
                            <Insets right="5.0" top="5.0" />
                        </FlowPane.margin>
                    </Button>
                </FlowPane>
            </VBox>
        </SplitPane>
    </center>
</BorderPane>
