## The FreeType Project: Freetype v2.13.2

### FreeType License
```

Copyright (C) 1996-2023 by <PERSON>, <PERSON>, and <PERSON>.
Copyright (C) 2007-2023 by <PERSON><PERSON> and <PERSON>.
Copyright (C) 1996-2023 by <PERSON>, <PERSON>, <PERSON>, and <PERSON>.
Copyright (C) 2022-2023 by <PERSON>, <PERSON>, <PERSON>, <PERSON>, and
Copyright (C) 2004-2023 by <PERSON><PERSON><PERSON> and Redhat K.K.
Copyright (C) 2007-2023 by <PERSON> and <PERSON>.
Copyright (C) 2003-2023 by <PERSON><PERSON><PERSON>, Red Hat K.K.,
Copyright (C) 1996-2023 by <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.
Copyright (C) 2007-2023 by <PERSON>.
Copyright (C) 2022-2023 by <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>.
Copyright (C) 2007-2023 by <PERSON><PERSON> <<EMAIL>>, <<EMAIL>>.
Copyright (C) 2008-2023 by <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON> to<PERSON>.
Copyright (C) 2013-2023 by Google, Inc.
Copyright (C) 2019-2023 by <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON>.
Copyright (C) 2009-2023 by <PERSON><PERSON> and <PERSON>.
Copyright (C) 2018-2023 by <PERSON>, <PERSON>, <PERSON>k R<PERSON>, and <PERSON> Lemberg.
Copyright (C) 2004-2023 by David <PERSON>, Robert <PERSON>, <PERSON> Le<PERSON>, and George Williams.


                    The FreeType Project LICENSE
                    ----------------------------

                            2006-<PERSON>-27

                    Copyright 1996-2002, 2006 by
          David <PERSON>, Robert Wilhelm, and Werner Le<PERSON>



Introduction
============

  The FreeType  Project is distributed in  several archive packages;
  some of them may contain, in addition to the FreeType font engine,
  various tools and  contributions which rely on, or  relate to, the
  FreeType Project.

  This  license applies  to all  files found  in such  packages, and
  which do not  fall under their own explicit  license.  The license
  affects  thus  the  FreeType   font  engine,  the  test  programs,
  documentation and makefiles, at the very least.

  This  license   was  inspired  by  the  BSD,   Artistic,  and  IJG
  (Independent JPEG  Group) licenses, which  all encourage inclusion
  and  use of  free  software in  commercial  and freeware  products
  alike.  As a consequence, its main points are that:

    o We don't promise that this software works. However, we will be
      interested in any kind of bug reports. (`as is' distribution)

    o You can  use this software for whatever you  want, in parts or
      full form, without having to pay us. (`royalty-free' usage)

    o You may not pretend that  you wrote this software.  If you use
      it, or  only parts of it,  in a program,  you must acknowledge
      somewhere  in  your  documentation  that  you  have  used  the
      FreeType code. (`credits')

  We  specifically  permit  and  encourage  the  inclusion  of  this
  software, with  or without modifications,  in commercial products.
  We  disclaim  all warranties  covering  The  FreeType Project  and
  assume no liability related to The FreeType Project.


  Finally,  many  people  asked  us  for  a  preferred  form  for  a
  credit/disclaimer to use in compliance with this license.  We thus
  encourage you to use the following text:

   """
    Portions of this software are copyright © <year> The FreeType
    Project (www.freetype.org).  All rights reserved.
   """

  Please replace <year> with the value from the FreeType version you
  actually use.


Legal Terms
===========

0. Definitions
--------------

  Throughout this license,  the terms `package', `FreeType Project',
  and  `FreeType  archive' refer  to  the  set  of files  originally
  distributed  by the  authors  (David Turner,  Robert Wilhelm,  and
  Werner Lemberg) as the `FreeType Project', be they named as alpha,
  beta or final release.

  `You' refers to  the licensee, or person using  the project, where
  `using' is a generic term including compiling the project's source
  code as  well as linking it  to form a  `program' or `executable'.
  This  program is  referred to  as  `a program  using the  FreeType
  engine'.

  This  license applies  to all  files distributed  in  the original
  FreeType  Project,   including  all  source   code,  binaries  and
  documentation,  unless  otherwise  stated   in  the  file  in  its
  original, unmodified form as  distributed in the original archive.
  If you are  unsure whether or not a particular  file is covered by
  this license, you must contact us to verify this.

  The FreeType  Project is copyright (C) 1996-2000  by David Turner,
  Robert Wilhelm, and Werner Lemberg.  All rights reserved except as
  specified below.

1. No Warranty
--------------

  THE FREETYPE PROJECT  IS PROVIDED `AS IS' WITHOUT  WARRANTY OF ANY
  KIND, EITHER  EXPRESS OR IMPLIED,  INCLUDING, BUT NOT  LIMITED TO,
  WARRANTIES  OF  MERCHANTABILITY   AND  FITNESS  FOR  A  PARTICULAR
  PURPOSE.  IN NO EVENT WILL ANY OF THE AUTHORS OR COPYRIGHT HOLDERS
  BE LIABLE  FOR ANY DAMAGES CAUSED  BY THE USE OR  THE INABILITY TO
  USE, OF THE FREETYPE PROJECT.

2. Redistribution
-----------------

  This  license  grants  a  worldwide, royalty-free,  perpetual  and
  irrevocable right  and license to use,  execute, perform, compile,
  display,  copy,   create  derivative  works   of,  distribute  and
  sublicense the  FreeType Project (in  both source and  object code
  forms)  and  derivative works  thereof  for  any  purpose; and  to
  authorize others  to exercise  some or all  of the  rights granted
  herein, subject to the following conditions:

    o Redistribution of  source code  must retain this  license file
      (`FTL.TXT') unaltered; any  additions, deletions or changes to
      the original  files must be clearly  indicated in accompanying
      documentation.   The  copyright   notices  of  the  unaltered,
      original  files must  be  preserved in  all  copies of  source
      files.

    o Redistribution in binary form must provide a  disclaimer  that
      states  that  the software is based in part of the work of the
      FreeType Team,  in  the  distribution  documentation.  We also
      encourage you to put an URL to the FreeType web page  in  your
      documentation, though this isn't mandatory.

  These conditions  apply to any  software derived from or  based on
  the FreeType Project,  not just the unmodified files.   If you use
  our work, you  must acknowledge us.  However, no  fee need be paid
  to us.

3. Advertising
--------------

  Neither the  FreeType authors and  contributors nor you  shall use
  the name of the  other for commercial, advertising, or promotional
  purposes without specific prior written permission.

  We suggest,  but do not require, that  you use one or  more of the
  following phrases to refer  to this software in your documentation
  or advertising  materials: `FreeType Project',  `FreeType Engine',
  `FreeType library', or `FreeType Distribution'.

  As  you have  not signed  this license,  you are  not  required to
  accept  it.   However,  as  the FreeType  Project  is  copyrighted
  material, only  this license, or  another one contracted  with the
  authors, grants you  the right to use, distribute,  and modify it.
  Therefore,  by  using,  distributing,  or modifying  the  FreeType
  Project, you indicate that you understand and accept all the terms
  of this license.

4. Contacts
-----------

  There are two mailing lists related to FreeType:

    o <EMAIL>

      Discusses general use and applications of FreeType, as well as
      future and  wanted additions to the  library and distribution.
      If  you are looking  for support,  start in  this list  if you
      haven't found anything to help you in the documentation.

    o <EMAIL>

      Discusses bugs,  as well  as engine internals,  design issues,
      specific licenses, porting, etc.

  Our home page can be found at

    https://www.freetype.org

```

### Additional Freetype Attributions
```

---------------------------------
The below license applies to the following files:
libfreetype/src/psaux/psarrst.c
libfreetype/src/psaux/psarrst.h
libfreetype/src/psaux/psblues.c
libfreetype/src/psaux/psblues.h
libfreetype/src/psaux/pserror.c
libfreetype/src/psaux/pserror.h
libfreetype/src/psaux/psfixed.h
libfreetype/src/psaux/psfont.c
libfreetype/src/psaux/psfont.h
libfreetype/src/psaux/psft.c
libfreetype/src/psaux/psft.h
libfreetype/src/psaux/psglue.h
libfreetype/src/psaux/pshints.c
libfreetype/src/psaux/pshints.h
libfreetype/src/psaux/psintrp.c
libfreetype/src/psaux/psintrp.h
libfreetype/src/psaux/psread.c
libfreetype/src/psaux/psread.h
libfreetype/src/psaux/psstack.c
libfreetype/src/psaux/psstack.h
libfreetype/src/psaux/pstypes.h

Copyright 2006-2014 Adobe Systems Incorporated.

This software, and all works of authorship, whether in source or
object code form as indicated by the copyright notice(s) included
herein (collectively, the "Work") is made available, and may only be
used, modified, and distributed under the FreeType Project License,
LICENSE.TXT.  Additionally, subject to the terms and conditions of the
FreeType Project License, each contributor to the Work hereby grants
to any individual or legal entity exercising permissions granted by
the FreeType Project License and this section (hereafter, "You" or
"Your") a perpetual, worldwide, non-exclusive, no-charge,
royalty-free, irrevocable (except as stated in this section) patent
license to make, have made, use, offer to sell, sell, import, and
otherwise transfer the Work, where such license applies only to those
patent claims licensable by such contributor that are necessarily
infringed by their contribution(s) alone or by combination of their
contribution(s) with the Work to which such contribution(s) was
submitted.  If You institute patent litigation against any entity
(including a cross-claim or counterclaim in a lawsuit) alleging that
the Work or a contribution incorporated within the Work constitutes
direct or contributory patent infringement, then any patent licenses
granted to You under this License for that Work shall terminate as of
the date such litigation is filed.

By using, modifying, or distributing the Work you indicate that you
have read and understood the terms and conditions of the
FreeType Project License as well as those provided in this section,
and you accept them fully.


```

### MIT License
```

---------------------------------
The below license applies to the following files:
libfreetype/include/freetype/internal/fthash.h
libfreetype/src/base/fthash.c

Copyright 2000 Computing Research Labs, New Mexico State University
Copyright 2001-2015

  Francesco Zappa Nardelli

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the "Software"),
to deal in the Software without restriction, including without limitation
the rights to use, copy, modify, merge, publish, distribute, sublicense,
and/or sell copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
THE COMPUTING RESEARCH LAB OR NEW MEXICO STATE UNIVERSITY BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT
OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR
THE USE OR OTHER DEALINGS IN THE SOFTWARE.


```
